<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Travel Offers Management</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f9f9f9;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: #030303;
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 10px;
        }

        .toolbar h2 {
            color: #333;
            font-size: 1.5rem;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #d71921;
            color: white;
        }

        .btn-primary:hover {
            background: #b8161c;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(215, 25, 33, 0.3);
        }

        .btn-secondary {
            background: #f9f9f9;
            color: #333;
            border: 2px solid #e1e5e9;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-1px);
        }

        .offers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .offer-card {
            background: #f9f9f9;
            border-radius: 15px;
            padding: 25px;
            border-left: 4px solid #d71921;
            transition: all 0.3s ease;
            position: relative;
        }

        .offer-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .offer-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .offer-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .offer-type-badge {
            background: #d71921;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .service-type-badge {
            background: #6c757d;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-left: 8px;
        }

        .offer-details {
            margin: 15px 0;
        }

        .offer-detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .offer-detail-label {
            color: #666;
            font-weight: 500;
        }

        .offer-detail-value {
            color: #333;
            font-weight: 600;
        }

        .offer-description {
            color: #666;
            font-size: 0.9rem;
            margin: 15px 0;
            line-height: 1.4;
        }

        .offer-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .btn-sm {
            padding: 8px 16px;
            font-size: 0.9rem;
        }

        .btn-edit {
            background: #17a2b8;
            color: white;
        }

        .btn-edit:hover {
            background: #138496;
        }

        .btn-delete {
            background: #dc3545;
            color: white;
        }

        .btn-delete:hover {
            background: #c82333;
        }

        .offer-status {
            position: absolute;
            top: 15px;
            right: 15px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-expired {
            background: #f8d7da;
            color: #721c24;
        }

        .status-draft {
            background: #fff3cd;
            color: #856404;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-state-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }

        .empty-state h3 {
            font-size: 1.5rem;
            margin-bottom: 10px;
            color: #333;
        }

        .empty-state p {
            font-size: 1rem;
            margin-bottom: 30px;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            animation: fadeIn 0.3s ease;
        }

        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 15px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
            animation: slideIn 0.3s ease;
        }

        .modal-header {
            background: #d71921;
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: 30px;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 5px;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        @media (max-width: 768px) {
            .offers-grid {
                grid-template-columns: 1fr;
            }
            
            .toolbar {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
        }

        .filters {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-group label {
            font-size: 0.9rem;
            color: #666;
            font-weight: 500;
        }

        .filter-group select {
            padding: 8px 12px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 0.9rem;
        }

        .filter-group select:focus {
            outline: none;
            border-color: #d71921;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Travel Offers Management</h1>
            <p>Manage all your travel offers in one place</p>
        </div>

        <div class="content">
            <!-- Toolbar -->
            <div class="toolbar">
                <h2>My Offers</h2>
                <button class="btn btn-primary" onclick="openOfferForm()">
                    ➕ Add New Offer
                </button>
            </div>

            <!-- Filters -->
            <div class="filters">
                <div class="filter-group">
                    <label for="serviceFilter">Service Type</label>
                    <select id="serviceFilter" onchange="filterOffers()">
                        <option value="">All Services</option>
                        <option value="flight">Flight</option>
                        <option value="hotel">Hotel</option>
                        <option value="package">Package</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="typeFilter">Offer Type</label>
                    <select id="typeFilter" onchange="filterOffers()">
                        <option value="">All Types</option>
                        <option value="loyalty_multiplier">Loyalty Multiplier</option>
                        <option value="bonus_points">Bonus Points</option>
                        <option value="absolute_discount">Absolute Discount</option>
                        <option value="percentage_discount">Percentage Discount</option>
                        <option value="additional_nights">Additional Nights</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="statusFilter">Status</label>
                    <select id="statusFilter" onchange="filterOffers()">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="expired">Expired</option>
                        <option value="draft">Draft</option>
                    </select>
                </div>
            </div>

            <!-- Offers Grid -->
            <div id="offersContainer">
                <div id="offersGrid" class="offers-grid">
                    <!-- Offers will be loaded here -->
                </div>
                <div id="emptyState" class="empty-state" style="display: none;">
                    <div class="empty-state-icon">📋</div>
                    <h3>No offers found</h3>
                    <p>Create your first travel offer to get started</p>
                    <button class="btn btn-primary" onclick="openOfferForm()">
                        Create Your First Offer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Confirm Delete</h3>
                <button class="close-btn" onclick="closeDeleteModal()">&times;</button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this offer? This action cannot be undone.</p>
                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                    <button class="btn btn-secondary" onclick="closeDeleteModal()">Cancel</button>
                    <button class="btn btn-delete" onclick="confirmDelete()">Delete</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Sample offers data - In real implementation, this would come from your API
        let offers = [
            {
                offerId: "offer_1704067200000",
                title: "Summer Flight Deal",
                serviceType: "flight",
                description: "Amazing summer flight deals to Europe",
                offerType: "percentage_discount",
                availability: {
                    validFrom: "2024-06-01",
                    validTo: "2024-08-31",
                    bookingDeadline: "2024-05-25",
                    maxRedemptions: 100
                },
                offerDetails: {
                    discountPercentage: 15,
                    maxDiscountCap: 200
                },
                targeting: {
                    serviceType: "flight",
                    route: {
                        departureCity: "New York (NYC)",
                        arrivalCity: "London (LHR)",
                        routeType: "direct",
                        destinations: ["London", "Paris", "Rome"]
                    },
                    timing: {
                        departureTimeType: "morning",
                        daysOfWeek: ["monday", "tuesday", "wednesday"]
                    },
                    cabinClass: ["economy", "premium_economy"],
                    fareClass: ["standard", "flexible"]
                },
                metadata: {
                    createdAt: "2024-01-01T10:00:00Z",
                    status: "active",
                    version: "1.0"
                }
            },
            {
                offerId: "offer_1704153600000",
                title: "Luxury Hotel Stay",
                serviceType: "hotel",
                description: "Extra nights at premium hotels",
                offerType: "additional_nights",
                availability: {
                    validFrom: "2024-07-01",
                    validTo: "2024-09-30",
                    bookingDeadline: "2024-06-25",
                    maxRedemptions: 50
                },
                offerDetails: {
                    freeNights: 2,
                    minimumStayRequired: 5
                },
                targeting: {
                    serviceType: "hotel",
                    location: {
                        destinations: ["Paris", "Rome", "Barcelona"],
                        specificProperties: ["Marriott Downtown", "Hilton Garden Inn"]
                    },
                    hotelChains: ["marriott", "hilton"],
                    starRating: ["4", "5"]
                },
                metadata: {
                    createdAt: "2024-01-02T10:00:00Z",
                    status: "active",
                    version: "1.0"
                }
            },
            {
                offerId: "offer_1704240000000",
                title: "Loyalty Bonus Campaign",
                serviceType: "flight",
                description: "Earn double points on all flights",
                offerType: "loyalty_multiplier",
                availability: {
                    validFrom: "2024-05-01",
                    validTo: "2024-05-31",
                    bookingDeadline: "2024-04-25",
                    maxRedemptions: 200
                },
                offerDetails: {
                    multiplierValue: 2
                },
                targeting: {
                    serviceType: "flight",
                    route: {
                        destinations: ["All destinations"]
                    },
                    cabinClass: ["business", "first"],
                    fareClass: ["flexible", "premium"]
                },
                metadata: {
                    createdAt: "2024-01-03T10:00:00Z",
                    status: "expired",
                    version: "1.0"
                }
            }
        ];

        let filteredOffers = [...offers];
        let deleteOfferId = null;

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            renderOffers();
        });

        function renderOffers() {
            const offersGrid = document.getElementById('offersGrid');
            const emptyState = document.getElementById('emptyState');

            if (filteredOffers.length === 0) {
                offersGrid.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            offersGrid.style.display = 'grid';
            emptyState.style.display = 'none';

            offersGrid.innerHTML = filteredOffers.map(offer => {
                const status = getOfferStatus(offer);
                const offerTypeDisplay = formatOfferType(offer.offerType);
                const serviceTypeDisplay = offer.serviceType.charAt(0).toUpperCase() + offer.serviceType.slice(1);
                
                return `
                    <div class="offer-card">
                        <div class="offer-status status-${status}">${status}</div>
                        <div class="offer-header">
                            <div>
                                <div class="offer-title">${offer.title}</div>
                                <div>
                                    <span class="offer-type-badge">${offerTypeDisplay}</span>
                                    <span class="service-type-badge">${serviceTypeDisplay}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="offer-description">
                            ${offer.description || 'No description provided'}
                        </div>
                        
                        <div class="offer-details">
                            <div class="offer-detail-row">
                                <span class="offer-detail-label">Valid Period:</span>
                                <span class="offer-detail-value">${formatDate(offer.availability.validFrom)} - ${formatDate(offer.availability.validTo)}</span>
                            </div>
                            <div class="offer-detail-row">
                                <span class="offer-detail-label">Max Redemptions:</span>
                                <span class="offer-detail-value">${offer.availability.maxRedemptions || 'Unlimited'}</span>
                            </div>
                            <div class="offer-detail-row">
                                <span class="offer-detail-label">Offer Value:</span>
                                <span class="offer-detail-value">${formatOfferValue(offer)}</span>
                            </div>
                        </div>
                        
                        <div class="offer-actions">
                            <button class="btn btn-edit btn-sm" onclick="editOffer('${offer.offerId}')">
                                ✏️ Edit
                            </button>
                            <button class="btn btn-delete btn-sm" onclick="deleteOffer('${offer.offerId}')">
                                🗑️ Delete
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function getOfferStatus(offer) {
            const now = new Date();
            const validFrom = new Date(offer.availability.validFrom);
            const validTo = new Date(offer.availability.validTo);
            
            if (offer.metadata.status === 'draft') return 'draft';
            if (now > validTo) return 'expired';
            if (now >= validFrom && now <= validTo) return 'active';
            return 'draft';
        }

        function formatOfferType(type) {
            const types = {
                'loyalty_multiplier': 'Loyalty Multiplier',
                'bonus_points': 'Bonus Points',
                'absolute_discount': 'Absolute Discount',
                'percentage_discount': 'Percentage Discount',
                'additional_nights': 'Additional Nights'
            };
            return types[type] || type;
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        function formatOfferValue(offer) {
            const details = offer.offerDetails;
            switch(offer.offerType) {
                case 'loyalty_multiplier':
                    return `${details.multiplierValue}x Points`;
                case 'bonus_points':
                    return `${details.bonusPoints} Points`;
                case 'absolute_discount':
                    return `${details.currency} ${details.discountAmount}`;
                case 'percentage_discount':
                    return `${details.discountPercentage}% (Max: ${details.maxDiscountCap || 'No limit'})`;
                case 'additional_nights':
                    return `${details.freeNights} Free Night(s)`;
                default:
                    return 'N/A';
            }
        }

        function filterOffers() {
            const serviceFilter = document.getElementById('serviceFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            filteredOffers = offers.filter(offer => {
                const status = getOfferStatus(offer);
                
                const serviceMatch = !serviceFilter || offer.serviceType === serviceFilter;
                const typeMatch = !typeFilter || offer.offerType === typeFilter;
                const statusMatch = !statusFilter || status === statusFilter;
                
                return serviceMatch && typeMatch && statusMatch;
            });

            renderOffers();
        }

        function openOfferForm(offerId = null) {
            // Construct URL for the offer form
            let url = 'offers.html'; // You would need to save the previous form as a separate file
            if (offerId) {
                url += `?edit=${offerId}`;
            }
            
            // Open in new window/tab
            window.open(url, '_blank');
            
            // Alternative: You could also embed the form in a modal here
            // For now, we'll just show an alert
            // if (offerId) {
            //     alert(`Opening edit form for offer: ${offerId}\n\nIn a real implementation, this would open the offer form with pre-filled data.`);
            // } else {
            //     alert('Opening new offer form...\n\nIn a real implementation, this would open the offer form in a clean state.');
            // }
        }

        function editOffer(offerId) {
            openOfferForm(offerId);
        }

        function deleteOffer(offerId) {
            deleteOfferId = offerId;
            document.getElementById('deleteModal').classList.add('active');
        }

        function closeDeleteModal() {
            document.getElementById('deleteModal').classList.remove('active');
            deleteOfferId = null;
        }

        function confirmDelete() {
            if (deleteOfferId) {
                offers = offers.filter(offer => offer.offerId !== deleteOfferId);
                filteredOffers = filteredOffers.filter(offer => offer.offerId !== deleteOfferId);
                renderOffers();
                closeDeleteModal();
                
                // In a real implementation, you would make an API call here
                console.log(`Deleted offer: ${deleteOfferId}`);
            }
        }

        // Sample function to add a new offer (for testing)
        function addSampleOffer() {
            const newOffer = {
                offerId: `offer_${Date.now()}`,
                title: "Test Offer",
                serviceType: "flight",
                description: "This is a test offer",
                offerType: "absolute_discount",
                availability: {
                    validFrom: "2024-06-01",
                    validTo: "2024-08-31",
                    maxRedemptions: 50
                },
                offerDetails: {
                    discountAmount: 100,
                    currency: "USD"
                },
                targeting: {
                    serviceType: "flight"
                },
                metadata: {
                    createdAt: new Date().toISOString(),
                    status: "active",
                    version: "1.0"
                }
            };
            
            offers.unshift(newOffer);
            filteredOffers = [...offers];
            renderOffers();
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('deleteModal');
            if (event.target === modal) {
                closeDeleteModal();
            }
        }
    </script>
</body>
</html>