<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Travel Offer Management</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f9f9f9;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: #030303;
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .form-container {
            padding: 40px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
        }

        .form-section {
            background: #f9f9f9;
            padding: 25px;
            border-radius: 15px;
            border-left: 4px solid #d71921;
        }

        .form-section.full-width {
            grid-column: 1 / -1;
        }

        .form-section h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #d71921;
            box-shadow: 0 0 0 3px rgba(215, 25, 33, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .form-row-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
        }

        .targeting-section {
            background: #f9f9f9;
            border-left: 4px solid #d71921;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            display: none;
        }

        .targeting-section.active {
            display: block;
        }

        .targeting-section h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .multi-select {
            position: relative;
        }

        .multi-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .multi-select-dropdown.active {
            display: block;
        }

        .multi-select-option {
            padding: 10px 15px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .multi-select-option:hover {
            background-color: #f8f9fa;
        }

        .multi-select-option.selected {
            background-color: #d71921;
            color: white;
        }

        .selected-items {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-top: 10px;
        }

        .selected-item {
            background: #d71921;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .remove-item {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-weight: bold;
        }

        .offer-type-details {
            background: #f9f9f9;
            border-left: 4px solid #d71921;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            display: none;
        }

        .offer-type-details.active {
            display: block;
        }

        .offer-type-details h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .action-buttons {
            display: flex;
            gap: 20px;
            margin-top: 40px;
            justify-content: center;
        }

        .btn {
            padding: 15px 40px;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 150px;
        }

        .btn-primary {
            background: #d71921;
            color: white;
            border: none;
        }

        .btn-primary:hover {
            background: #b8161c;
        }

        .btn-secondary {
            background: #f9f9f9;
            color: #333;
            border: 2px solid #e1e5e9;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .json-output {
            background: #333;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 400px;
            overflow-y: auto;
            border-left: 4px solid #d71921;
        }

        .json-output pre {
            margin: 0;
            white-space: pre-wrap;
        }

        .success-message {
            background: #f9f9f9;
            color: #333;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            border-left: 4px solid #d71921;
            display: none;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }

            .form-row,
            .form-row-3 {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
        }

        .time-range {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-item input[type="checkbox"] {
            width: auto;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✈️ Travel Offer Management</h1>
            <p>Create targeted travel offers with advanced filtering options</p>
        </div>

        <div class="form-container">
            <form id="offerForm">
                <div class="form-grid">
                    <!-- Basic Information -->
                    <div class="form-section">
                        <h3>📋 Basic Information</h3>
                        <div class="form-group">
                            <label for="offerTitle">Offer Title</label>
                            <input type="text" id="offerTitle" name="offerTitle" placeholder="Amazing Flight Deal" required>
                        </div>
                        <div class="form-group">
                            <label for="serviceType">Service Type</label>
                            <select id="serviceType" name="serviceType" required onchange="toggleTargetingSection()">
                                <option value="">Select Service Type</option>
                                <option value="flight">Flight</option>
                                <option value="hotel">Hotel</option>
                                <option value="package">Package Deal</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea id="description" name="description" placeholder="Describe your offer..."></textarea>
                        </div>
                    </div>

                    <!-- Offer Type & Details -->
                    <div class="form-section">
                        <h3>🎯 Offer Type & Value</h3>
                        <div class="form-group">
                            <label for="offerType">Offer Type</label>
                            <select id="offerType" name="offerType" required onchange="toggleOfferTypeDetails()">
                                <option value="">Select Offer Type</option>
                                <option value="loyalty_multiplier">Loyalty Multiplier</option>
                                <option value="bonus_points">Bonus Loyalty Points</option>
                                <option value="absolute_discount">Absolute Discount</option>
                                <option value="percentage_discount">Percentage Discount</option>
                                <option value="additional_nights" id="additionalNightsOption" style="display: none;">Additional Nights (Hotels Only)</option>
                            </select>
                        </div>

                        <!-- Offer Type Specific Details -->
                        <div id="loyaltyMultiplierDetails" class="offer-type-details">
                            <h4>Loyalty Multiplier Settings</h4>
                            <div class="form-group">
                                <label for="multiplierValue">Multiplier Value (e.g., 2x, 3x)</label>
                                <input type="number" id="multiplierValue" name="multiplierValue" step="0.1" min="1" placeholder="2">
                            </div>
                        </div>

                        <div id="bonusPointsDetails" class="offer-type-details">
                            <h4>Bonus Points Settings</h4>
                            <div class="form-group">
                                <label for="bonusPoints">Bonus Points Amount</label>
                                <input type="number" id="bonusPoints" name="bonusPoints" min="1" placeholder="1000">
                            </div>
                        </div>

                        <div id="absoluteDiscountDetails" class="offer-type-details">
                            <h4>Absolute Discount Settings</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="discountAmount">Discount Amount</label>
                                    <input type="number" id="discountAmount" name="discountAmount" step="0.01" placeholder="50.00">
                                </div>
                                <div class="form-group">
                                    <label for="discountCurrency">Currency</label>
                                    <select id="discountCurrency" name="discountCurrency">
                                        <option value="USD">USD</option>
                                        <option value="EUR">EUR</option>
                                        <option value="GBP">GBP</option>
                                        <option value="INR">INR</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div id="percentageDiscountDetails" class="offer-type-details">
                            <h4>Percentage Discount Settings</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="discountPercentage">Discount Percentage</label>
                                    <input type="number" id="discountPercentage" name="discountPercentage" min="1" max="100" placeholder="15">
                                </div>
                                <div class="form-group">
                                    <label for="discountCap">Maximum Discount Cap</label>
                                    <input type="number" id="discountCap" name="discountCap" step="0.01" placeholder="200.00">
                                </div>
                            </div>
                        </div>

                        <div id="additionalNightsDetails" class="offer-type-details">
                            <h4>Additional Nights Settings</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="minimumStay">Minimum Nights to Book</label>
                                    <input type="number" id="minimumStay" name="minimumStay" min="1" placeholder="3">
                                </div>
                                <div class="form-group">
                                    <label for="freeNights">Free Nights Offered</label>
                                    <input type="number" id="freeNights" name="freeNights" min="1" placeholder="1">
                                </div>
                            </div>
                            <div class="form-group">
                                <label>Example: Book 3 nights, get 1 night free</label>
                            </div>
                        </div>
                    </div>

                    <!-- Availability & Dates -->
                    <div class="form-section">
                        <h3>📅 Availability & Dates</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="validFrom">Valid From</label>
                                <input type="date" id="validFrom" name="validFrom" required>
                            </div>
                            <div class="form-group">
                                <label for="validTo">Valid To</label>
                                <input type="date" id="validTo" name="validTo" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="bookingDeadline">Booking Deadline</label>
                                <input type="date" id="bookingDeadline" name="bookingDeadline">
                            </div>
                            <div class="form-group">
                                <label for="maxRedemptions">Maximum Redemptions</label>
                                <input type="number" id="maxRedemptions" name="maxRedemptions" min="1" placeholder="100">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Flight Targeting Section -->
                <div id="flightTargeting" class="targeting-section">
                    <h4>✈️ Flight Targeting Options</h4>
                    <div class="form-grid">
                        <div class="form-section">
                            <h3>🛫 Route & Destination</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="departureCity">Departure City/Airport</label>
                                    <input type="text" id="departureCity" name="departureCity" placeholder="New York (NYC)">
                                </div>
                                <div class="form-group">
                                    <label for="arrivalCity">Arrival City/Airport</label>
                                    <input type="text" id="arrivalCity" name="arrivalCity" placeholder="London (LHR)">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="routeType">Route Type</label>
                                <select id="routeType" name="routeType">
                                    <option value="">Any Route</option>
                                    <option value="direct">Direct Flights Only</option>
                                    <option value="one_stop">One Stop Maximum</option>
                                    <option value="any">Any Route</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="destinations">Specific Destinations</label>
                                <textarea id="destinations" name="destinations" placeholder="Enter destinations separated by commas (e.g., Paris, Rome, Barcelona)"></textarea>
                            </div>
                        </div>

                        <div class="form-section">
                            <h3>⏰ Flight Timing</h3>
                            <div class="form-group">
                                <label for="departureTimeType">Departure Time Preference</label>
                                <select id="departureTimeType" name="departureTimeType">
                                    <option value="">Any Time</option>
                                    <option value="morning">Morning (06:00 - 12:00)</option>
                                    <option value="afternoon">Afternoon (12:00 - 18:00)</option>
                                    <option value="evening">Evening (18:00 - 24:00)</option>
                                    <option value="night">Night (00:00 - 06:00)</option>
                                    <option value="custom">Custom Time Range</option>
                                </select>
                            </div>
                            <div id="customTimeRange" class="time-range" style="display: none;">
                                <div class="form-group">
                                    <label for="departureFrom">Departure From</label>
                                    <input type="time" id="departureFrom" name="departureFrom">
                                </div>
                                <div class="form-group">
                                    <label for="departureTo">Departure To</label>
                                    <input type="time" id="departureTo" name="departureTo">
                                </div>
                            </div>
                            <div class="form-group">
                                <label>Days of Week</label>
                                <div class="checkbox-group">
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="monday" name="daysOfWeek" value="monday">
                                        <label for="monday">Monday</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="tuesday" name="daysOfWeek" value="tuesday">
                                        <label for="tuesday">Tuesday</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="wednesday" name="daysOfWeek" value="wednesday">
                                        <label for="wednesday">Wednesday</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="thursday" name="daysOfWeek" value="thursday">
                                        <label for="thursday">Thursday</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="friday" name="daysOfWeek" value="friday">
                                        <label for="friday">Friday</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="saturday" name="daysOfWeek" value="saturday">
                                        <label for="saturday">Saturday</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="sunday" name="daysOfWeek" value="sunday">
                                        <label for="sunday">Sunday</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <h3>🛋️ Cabin & Fare Class</h3>
                            <div class="form-group">
                                <label>Cabin Class</label>
                                <div class="checkbox-group">
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="economy" name="cabinClass" value="economy">
                                        <label for="economy">Economy</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="premium_economy" name="cabinClass" value="premium_economy">
                                        <label for="premium_economy">Premium Economy</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="business" name="cabinClass" value="business">
                                        <label for="business">Business</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="first" name="cabinClass" value="first">
                                        <label for="first">First Class</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>Fare Class</label>
                                <div class="checkbox-group">
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="basic" name="fareClass" value="basic">
                                        <label for="basic">Basic/Restricted</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="standard" name="fareClass" value="standard">
                                        <label for="standard">Standard</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="flexible" name="fareClass" value="flexible">
                                        <label for="flexible">Flexible/Refundable</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="premium" name="fareClass" value="premium">
                                        <label for="premium">Premium/Flex</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hotel Targeting Section -->
                <div id="hotelTargeting" class="targeting-section">
                    <h4>🏨 Hotel Targeting Options</h4>
                    <div class="form-grid">
                        <div class="form-section">
                            <h3>📍 Location & Property</h3>
                            <div class="form-group">
                                <label for="hotelDestinations">Destinations</label>
                                <textarea id="hotelDestinations" name="hotelDestinations" placeholder="Enter destinations separated by commas (e.g., Paris, Rome, Barcelona)"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="specificProperties">Specific Properties</label>
                                <textarea id="specificProperties" name="specificProperties" placeholder="Enter hotel names separated by commas (e.g., Marriott Downtown, Hilton Garden Inn)"></textarea>
                            </div>
                        </div>

                        <div class="form-section">
                            <h3>🏢 Hotel Chains & Categories</h3>
                            <div class="form-group">
                                <label>Hotel Chains</label>
                                <div class="checkbox-group">
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="marriott" name="hotelChains" value="marriott">
                                        <label for="marriott">Marriott</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="hilton" name="hotelChains" value="hilton">
                                        <label for="hilton">Hilton</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="hyatt" name="hotelChains" value="hyatt">
                                        <label for="hyatt">Hyatt</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="ihg" name="hotelChains" value="ihg">
                                        <label for="ihg">IHG</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="accor" name="hotelChains" value="accor">
                                        <label for="accor">Accor</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="radisson" name="hotelChains" value="radisson">
                                        <label for="radisson">Radisson</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="independent" name="hotelChains" value="independent">
                                        <label for="independent">Independent Hotels</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>Hotel Star Rating</label>
                                <div class="checkbox-group">
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="star3" name="starRating" value="3">
                                        <label for="star3">3 Star</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="star4" name="starRating" value="4">
                                        <label for="star4">4 Star</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="star5" name="starRating" value="5">
                                        <label for="star5">5 Star</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="luxury" name="starRating" value="luxury">
                                        <label for="luxury">Luxury</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <button type="button" class="btn btn-secondary" onclick="previewOffer()">
                        🔍 Preview JSON
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="goBackToListing()" id="backButton" style="display: none;">
                        ← Back to List
                    </button>
                    <button type="submit" class="btn btn-primary">
                        💾 Save Offer
                    </button>
                </div>
            </form>

            <!-- Success Message -->
            <div id="successMessage" class="success-message">
                ✅ Offer saved successfully!
            </div>

            <!-- JSON Output -->
            <div id="jsonOutput" class="json-output" style="display: none;">
                <pre id="jsonContent"></pre>
            </div>
        </div>
    </div>

    <script>
        // Initialize form with today's date
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('validFrom').value = today;

            const nextMonth = new Date();
            nextMonth.setMonth(nextMonth.getMonth() + 1);
            document.getElementById('validTo').value = nextMonth.toISOString().split('T')[0];

            // Set booking deadline to 7 days before valid from
            const bookingDeadline = new Date();
            bookingDeadline.setDate(bookingDeadline.getDate() - 7);
            document.getElementById('bookingDeadline').value = bookingDeadline.toISOString().split('T')[0];
        });

        function toggleTargetingSection() {
            const serviceType = document.getElementById('serviceType').value;
            const flightTargeting = document.getElementById('flightTargeting');
            const hotelTargeting = document.getElementById('hotelTargeting');
            const additionalNightsOption = document.getElementById('additionalNightsOption');
            const offerTypeSelect = document.getElementById('offerType');

            // Hide all targeting sections
            flightTargeting.classList.remove('active');
            hotelTargeting.classList.remove('active');

            // Show/hide additional nights option based on service type
            if (serviceType === 'hotel') {
                additionalNightsOption.style.display = 'block';
                hotelTargeting.classList.add('active');
            } else {
                additionalNightsOption.style.display = 'none';
                // Reset offer type if additional nights was selected for non-hotel service
                if (offerTypeSelect.value === 'additional_nights') {
                    offerTypeSelect.value = '';
                    toggleOfferTypeDetails();
                }
                if (serviceType === 'flight') {
                    flightTargeting.classList.add('active');
                }
            }
        }

        function toggleOfferTypeDetails() {
            const offerType = document.getElementById('offerType').value;

            // Hide all offer type details
            document.querySelectorAll('.offer-type-details').forEach(section => {
                section.classList.remove('active');
            });

            // Show relevant offer type details
            const detailsMap = {
                'loyalty_multiplier': 'loyaltyMultiplierDetails',
                'bonus_points': 'bonusPointsDetails',
                'absolute_discount': 'absoluteDiscountDetails',
                'percentage_discount': 'percentageDiscountDetails',
                'additional_nights': 'additionalNightsDetails'
            };

            if (detailsMap[offerType]) {
                document.getElementById(detailsMap[offerType]).classList.add('active');
            }
        }

        // Toggle custom time range - with safety check
        function setupTimeRangeToggle() {
            const departureTimeType = document.getElementById('departureTimeType');
            if (departureTimeType) {
                departureTimeType.addEventListener('change', function() {
                    const customTimeRange = document.getElementById('customTimeRange');
                    if (customTimeRange) {
                        if (this.value === 'custom') {
                            customTimeRange.style.display = 'grid';
                        } else {
                            customTimeRange.style.display = 'none';
                        }
                    }
                });
            }
        }

        function getCheckedValues(name) {
            const checkboxes = document.querySelectorAll(`input[name="${name}"]:checked`);
            return Array.from(checkboxes).map(cb => cb.value);
        }

        let isEditMode = false;
        let currentOfferId = null;

        // Enhanced DOMContentLoaded with edit mode support
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date().toISOString().split('T')[0];
            const validFromElement = document.getElementById('validFrom');
            const validToElement = document.getElementById('validTo');
            const bookingDeadlineElement = document.getElementById('bookingDeadline');

            if (validFromElement) validFromElement.value = today;

            if (validToElement) {
                const nextMonth = new Date();
                nextMonth.setMonth(nextMonth.getMonth() + 1);
                validToElement.value = nextMonth.toISOString().split('T')[0];
            }

            if (bookingDeadlineElement) {
                const bookingDeadline = new Date();
                bookingDeadline.setDate(bookingDeadline.getDate() - 7);
                bookingDeadlineElement.value = bookingDeadline.toISOString().split('T')[0];
            }

            setupTimeRangeToggle();
            checkEditMode();
        });

        function checkEditMode() {
            const urlParams = new URLSearchParams(window.location.search);
            const editOfferId = urlParams.get('edit');

            if (editOfferId) {
                isEditMode = true;
                currentOfferId = editOfferId;
                loadOfferData(editOfferId);

                document.title = 'Edit Travel Offer';
                document.querySelector('.header h1').textContent = '✏️ Edit Travel Offer';
                document.querySelector('.header p').textContent = 'Modify your existing travel offer';
                document.querySelector('.btn-primary').textContent = '💾 Update Offer';

                document.getElementById('backButton').style.display = 'inline-flex';
            } else {
                document.title = 'Create Travel Offer';
                document.querySelector('.header h1').textContent = '✈️ Create New Travel Offer';
                document.querySelector('.header p').textContent = 'Create targeted travel offers with advanced filtering options';

            }
        }

        function goBackToListing() {
            if (confirm('Are you sure you want to go back? Any unsaved changes will be lost.')) {
                window.location.href = 'offer-listing.html';
            }
        }

        async function loadOfferData(offerId) {
            try {
                showLoadingState(true);
                const offerData = await simulateOfferLoad(offerId);

                if (offerData) {
                    populateForm(offerData);
                } else {
                    showError('Offer not found');
                }
            } catch (error) {
                console.error('Error loading offer:', error);
                showError('Failed to load offer data');
            } finally {
                showLoadingState(false);
            }
        }

        async function simulateOfferLoad(offerId) {
            await new Promise(resolve => setTimeout(resolve, 1000));

            const sampleOffers = {
                'offer_1704067200000': {
                    offerId: "offer_1704067200000",
                    title: "Summer Flight Deal",
                    serviceType: "flight",
                    description: "Amazing summer flight deals to Europe",
                    offerType: "percentage_discount",
                    availability: {
                        validFrom: "2024-06-01",
                        validTo: "2024-08-31",
                        bookingDeadline: "2024-05-25",
                        maxRedemptions: 100
                    },
                    offerDetails: {
                        discountPercentage: 15,
                        maxDiscountCap: 200
                    },
                    targeting: {
                        serviceType: "flight",
                        route: {
                            departureCity: "New York (NYC)",
                            arrivalCity: "London (LHR)",
                            routeType: "direct",
                            destinations: "London, Paris, Rome"
                        },
                        timing: {
                            departureTimeType: "morning",
                            daysOfWeek: ["monday", "tuesday", "wednesday"]
                        },
                        cabinClass: ["economy", "premium_economy"],
                        fareClass: ["standard", "flexible"]
                    }
                }
            };

            return sampleOffers[offerId] || null;
        }

        function populateForm(offerData) {
            document.getElementById('offerTitle').value = offerData.title || '';
            document.getElementById('serviceType').value = offerData.serviceType || '';
            document.getElementById('description').value = offerData.description || '';
            document.getElementById('offerType').value = offerData.offerType || '';

            if (offerData.availability) {
                document.getElementById('validFrom').value = offerData.availability.validFrom || '';
                document.getElementById('validTo').value = offerData.availability.validTo || '';
                document.getElementById('bookingDeadline').value = offerData.availability.bookingDeadline || '';
                document.getElementById('maxRedemptions').value = offerData.availability.maxRedemptions || '';
            }

            populateOfferDetails(offerData.offerType, offerData.offerDetails);
            toggleTargetingSection();

            if (offerData.targeting) {
                populateTargetingData(offerData.targeting);
            }

            toggleOfferTypeDetails();
        }

        function populateOfferDetails(offerType, details) {
            if (!details) return;

            switch(offerType) {
                case 'loyalty_multiplier':
                    document.getElementById('multiplierValue').value = details.multiplierValue || '';
                    break;
                case 'bonus_points':
                    document.getElementById('bonusPoints').value = details.bonusPoints || '';
                    break;
                case 'absolute_discount':
                    document.getElementById('discountAmount').value = details.discountAmount || '';
                    document.getElementById('discountCurrency').value = details.currency || 'USD';
                    break;
                case 'percentage_discount':
                    document.getElementById('discountPercentage').value = details.discountPercentage || '';
                    document.getElementById('discountCap').value = details.maxDiscountCap || '';
                    break;
                case 'additional_nights':
                    document.getElementById('freeNights').value = details.freeNights || '';
                    document.getElementById('minimumStay').value = details.minimumStayRequired || '';
                    break;
            }
        }

        function populateTargetingData(targeting) {
            if (targeting.serviceType === 'flight') {
                if (targeting.route) {
                    if (targeting.route.departureCity) document.getElementById('departureCity').value = targeting.route.departureCity;
                    if (targeting.route.arrivalCity) document.getElementById('arrivalCity').value = targeting.route.arrivalCity;
                    if (targeting.route.routeType) document.getElementById('routeType').value = targeting.route.routeType;
                    if (targeting.route.destinations) {
                        const destinations = Array.isArray(targeting.route.destinations) ?
                            targeting.route.destinations.join(', ') : targeting.route.destinations;
                        document.getElementById('destinations').value = destinations;
                    }
                }

                if (targeting.timing) {
                    if (targeting.timing.departureTimeType) {
                        document.getElementById('departureTimeType').value = targeting.timing.departureTimeType;

                        if (targeting.timing.departureTimeType === 'custom' && targeting.timing.customTimeRange) {
                            if (targeting.timing.customTimeRange.from) document.getElementById('departureFrom').value = targeting.timing.customTimeRange.from;
                            if (targeting.timing.customTimeRange.to) document.getElementById('departureTo').value = targeting.timing.customTimeRange.to;
                            document.getElementById('customTimeRange').style.display = 'grid';
                        }
                    }

                    if (targeting.timing.daysOfWeek && Array.isArray(targeting.timing.daysOfWeek)) {
                        targeting.timing.daysOfWeek.forEach(day => {
                            const checkbox = document.getElementById(day);
                            if (checkbox) checkbox.checked = true;
                        });
                    }
                }

                if (targeting.cabinClass && Array.isArray(targeting.cabinClass)) {
                    targeting.cabinClass.forEach(cabin => {
                        const checkbox = document.getElementById(cabin);
                        if (checkbox) checkbox.checked = true;
                    });
                }

                if (targeting.fareClass && Array.isArray(targeting.fareClass)) {
                    targeting.fareClass.forEach(fare => {
                        const checkbox = document.getElementById(fare);
                        if (checkbox) checkbox.checked = true;
                    });
                }
            } else if (targeting.serviceType === 'hotel') {
                if (targeting.location) {
                    if (targeting.location.destinations) {
                        const destinations = Array.isArray(targeting.location.destinations) ?
                            targeting.location.destinations.join(', ') : targeting.location.destinations;
                        document.getElementById('hotelDestinations').value = destinations;
                    }
                    if (targeting.location.specificProperties) {
                        const properties = Array.isArray(targeting.location.specificProperties) ?
                            targeting.location.specificProperties.join(', ') : targeting.location.specificProperties;
                        document.getElementById('specificProperties').value = properties;
                    }
                }

                if (targeting.hotelChains && Array.isArray(targeting.hotelChains)) {
                    targeting.hotelChains.forEach(chain => {
                        const checkbox = document.getElementById(chain);
                        if (checkbox) checkbox.checked = true;
                    });
                }

                if (targeting.starRating && Array.isArray(targeting.starRating)) {
                    targeting.starRating.forEach(rating => {
                        const checkbox = document.getElementById('star' + rating);
                        if (checkbox) checkbox.checked = true;
                    });
                }
            }
        }

        function showLoadingState(show) {
            const loadingOverlay = document.getElementById('loadingOverlay');

            if (show) {
                if (!loadingOverlay) {
                    const overlay = document.createElement('div');
                    overlay.id = 'loadingOverlay';
                    overlay.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(255, 255, 255, 0.8); display: flex; align-items: center; justify-content: center; z-index: 9999; font-size: 1.2rem; color: #333;';
                    overlay.innerHTML = '<div>🔄 Loading offer data...</div>';
                    document.body.appendChild(overlay);
                }
            } else {
                if (loadingOverlay) {
                    loadingOverlay.remove();
                }
            }
        }

        function showError(message) {
            alert('Error: ' + message);
        }



        function generateOfferJSON() {
            const form = document.getElementById('offerForm');
            const formData = new FormData(form);

            const offer = {
                offerId: isEditMode ? currentOfferId : 'offer_' + Date.now(),
                title: formData.get('offerTitle'),
                serviceType: formData.get('serviceType'),
                description: formData.get('description'),
                offerType: formData.get('offerType'),
                availability: {
                    validFrom: formData.get('validFrom'),
                    validTo: formData.get('validTo'),
                    bookingDeadline: formData.get('bookingDeadline'),
                    maxRedemptions: parseInt(formData.get('maxRedemptions')) || null
                },
                offerDetails: {},
                targeting: {},
                metadata: {
                    createdAt: isEditMode ? undefined : new Date().toISOString(),
                    updatedAt: isEditMode ? new Date().toISOString() : undefined,
                    status: 'active',
                    version: '1.0'
                }
            };

            if (offer.metadata.createdAt === undefined) delete offer.metadata.createdAt;
            if (offer.metadata.updatedAt === undefined) delete offer.metadata.updatedAt;

            const offerType = formData.get('offerType');
            switch(offerType) {
                case 'loyalty_multiplier':
                    offer.offerDetails = {
                        multiplierValue: parseFloat(formData.get('multiplierValue')) || 2
                    };
                    break;
                case 'bonus_points':
                    offer.offerDetails = {
                        bonusPoints: parseInt(formData.get('bonusPoints')) || 0
                    };
                    break;
                case 'absolute_discount':
                    offer.offerDetails = {
                        discountAmount: parseFloat(formData.get('discountAmount')) || 0,
                        currency: formData.get('discountCurrency') || 'USD'
                    };
                    break;
                case 'percentage_discount':
                    offer.offerDetails = {
                        discountPercentage: parseFloat(formData.get('discountPercentage')) || 0,
                        maxDiscountCap: parseFloat(formData.get('discountCap')) || null
                    };
                    break;
                case 'additional_nights':
                    offer.offerDetails = {
                        freeNights: parseInt(formData.get('freeNights')) || 1,
                        minimumStayRequired: parseInt(formData.get('minimumStay')) || 3
                    };
                    break;
            }

            const serviceType = formData.get('serviceType');
            if (serviceType === 'flight') {
                offer.targeting = {
                    serviceType: 'flight',
                    route: {
                        departureCity: formData.get('departureCity') || null,
                        arrivalCity: formData.get('arrivalCity') || null,
                        routeType: formData.get('routeType') || null,
                        destinations: formData.get('destinations') ?
                            formData.get('destinations').split(',').map(d => d.trim()) : []
                    },
                    timing: {
                        departureTimeType: formData.get('departureTimeType') || null,
                        customTimeRange: formData.get('departureTimeType') === 'custom' ? {
                            from: formData.get('departureFrom'),
                            to: formData.get('departureTo')
                        } : null,
                        daysOfWeek: getCheckedValues('daysOfWeek')
                    },
                    cabinClass: getCheckedValues('cabinClass'),
                    fareClass: getCheckedValues('fareClass')
                };
            } else if (serviceType === 'hotel') {
                offer.targeting = {
                    serviceType: 'hotel',
                    location: {
                        destinations: formData.get('hotelDestinations') ?
                            formData.get('hotelDestinations').split(',').map(d => d.trim()) : [],
                        specificProperties: formData.get('specificProperties') ?
                            formData.get('specificProperties').split(',').map(p => p.trim()) : []
                    },
                    hotelChains: getCheckedValues('hotelChains'),
                    starRating: getCheckedValues('starRating')
                };
            }

            return offer;
        }

        function previewOffer() {
            const offer = generateOfferJSON();
            const jsonOutput = document.getElementById('jsonOutput');
            const jsonContent = document.getElementById('jsonContent');

            jsonContent.textContent = JSON.stringify(offer, null, 2);
            jsonOutput.style.display = 'block';
            jsonOutput.scrollIntoView({ behavior: 'smooth' });
        }

        async function submitOffer(offerData) {
            console.log(isEditMode ? 'Updating offer:' : 'Creating offer:', offerData);

            return new Promise((resolve) => {
                setTimeout(() => {
                    resolve({
                        success: true,
                        offerId: offerData.offerId,
                        message: isEditMode ? 'Offer updated successfully' : 'Offer created successfully'
                    });
                }, 1000);
            });
        }

        document.getElementById('offerForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const offer = generateOfferJSON();

            try {
                const button = document.querySelector('.btn-primary');
                button.textContent = isEditMode ? '⏳ Updating...' : '⏳ Saving...';
                button.disabled = true;

                const result = await submitOffer(offer);

                if (result.success) {
                    document.getElementById('successMessage').textContent =
                        '✅ ' + (isEditMode ? 'Offer updated successfully!' : 'Offer saved successfully!');
                    document.getElementById('successMessage').style.display = 'block';
                    document.getElementById('successMessage').scrollIntoView({ behavior: 'smooth' });

                    previewOffer();

                    setTimeout(() => {
                        const message = isEditMode ?
                            'Offer updated successfully! Would you like to continue editing or return to the offers list?' :
                            'Offer saved successfully! Would you like to create another offer?';

                        if (confirm(message)) {
                            if (isEditMode) {
                                return;
                            } else {
                                document.getElementById('offerForm').reset();
                                document.getElementById('jsonOutput').style.display = 'none';
                                document.getElementById('successMessage').style.display = 'none';
                                document.querySelectorAll('.targeting-section').forEach(section => {
                                    section.classList.remove('active');
                                });
                                document.querySelectorAll('.offer-type-details').forEach(section => {
                                    section.classList.remove('active');
                                });
                            }
                        } else {
                            window.location.href = 'offer-listing.html';
                        }
                    }, 3000);
                }
            } catch (error) {
                alert('Error saving offer: ' + error.message);
            } finally {
                const button = document.querySelector('.btn-primary');
                button.textContent = isEditMode ? '💾 Update Offer' : '💾 Save Offer';
                button.disabled = false;
            }
        });
    </script>
</body>
</html>